package au.com.allianceautomation.iython;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

/**
 * Demo test to show improved import traceback functionality.
 * 
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
class TracebackDemoTest {
    
    @Test
    @DisplayName("Demo: Import traceback improvements")
    void demoImportTracebacks() {
        PythonExecutor executor = new PythonExecutor();
        
        System.out.println("=== Import Traceback Demo ===");
        
        // Test _pydatetime import
        System.out.println("\n1. Testing 'import _pydatetime':");
        try {
            executor.executeCode("import _pydatetime");
        } catch (PythonExecutionException e) {
            System.out.println(e.getMessage());
        }
        
        // Test _datetime import
        System.out.println("\n2. Testing 'import _datetime':");
        try {
            executor.executeCode("import _datetime");
        } catch (PythonExecutionException e) {
            System.out.println(e.getMessage());
        }
        
        // Test nonexistent module import
        System.out.println("\n3. Testing 'import nonexistent_module':");
        try {
            executor.executeCode("import nonexistent_module");
        } catch (PythonExecutionException e) {
            System.out.println(e.getMessage());
        }
        
        // Test from import
        System.out.println("\n4. Testing 'from _datetime import date':");
        try {
            executor.executeCode("from _datetime import date");
        } catch (PythonExecutionException e) {
            System.out.println(e.getMessage());
        }
        
        System.out.println("\n=== Demo Complete ===");
        
        executor.close();
    }
}
