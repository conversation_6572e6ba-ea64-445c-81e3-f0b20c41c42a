#!/usr/bin/env python3

# Test script to verify import traceback behavior
print("Testing import traceback behavior...")

try:
    import _pydatetime
except Exception as e:
    print(f"_pydatetime import failed: {e}")

try:
    import _datetime
except Exception as e:
    print(f"_datetime import failed: {e}")

try:
    import nonexistent_module
except Exception as e:
    print(f"nonexistent_module import failed: {e}")

print("Test completed.")
