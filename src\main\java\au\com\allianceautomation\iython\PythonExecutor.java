package au.com.allianceautomation.iython;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import org.antlr.v4.runtime.CharStream;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import au.com.allianceautomation.iython.ast.Program;
import au.com.allianceautomation.iython.parser.Python3Lexer;
import au.com.allianceautomation.iython.parser.Python3Parser;
import au.com.allianceautomation.iython.parser.PythonASTBuilder;
import au.com.allianceautomation.iython.runtime.PythonRuntime;

/**
 * Handles execution of Python code using our custom Python interpreter within the JVM.
 * Uses ANTLR4 for parsing and Byte-Buddy for runtime bytecode generation.
 *
 * <AUTHOR> Automation Australia
 * @version 1.0.0
 */
public class PythonExecutor {

    private static final Logger logger = LoggerFactory.getLogger(PythonExecutor.class);

    private final PythonRuntime runtime;
    private final Map<String, Object> globalVariables;

    public PythonExecutor() {
        logger.debug("Initializing custom Python executor");

        this.runtime = new PythonRuntime();
        this.globalVariables = new HashMap<>();

        logger.debug("Python executor initialized successfully");
    }
    
    /**
     * Execute Python code from a string.
     *
     * @param code The Python code to execute
     * @return The output from the Python execution
     * @throws PythonExecutionException if execution fails
     */
    public String executeCode(String code) throws PythonExecutionException {
        logger.debug("Executing Python code: {}", code.substring(0, Math.min(code.length(), 100)));

        try {
            // Parse the Python code into an AST
            Program program = parseCode(code);

            // Execute the AST using our runtime
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PrintStream printStream = new PrintStream(outputStream);

            String output = runtime.execute(program, globalVariables, printStream);
            logger.debug("Python execution completed. Output length: {} characters", output.length());

            return output;

        } catch (au.com.allianceautomation.iython.exceptions.ModuleNotFoundError e) {
            // Preserve ModuleNotFoundError with their formatted messages
            logger.debug("Module not found error", e);
            throw new PythonExecutionException(e.formatPythonException(), e);
        } catch (au.com.allianceautomation.iython.runtime.exceptions.PythonRuntimeException e) {
            // Preserve Python runtime exceptions with their formatted messages
            logger.debug("Python runtime error", e);
            throw new PythonExecutionException(e.formatPythonException(), e);
        } catch (Exception e) {
            logger.error("Error executing Python code", e);
            throw new PythonExecutionException("Failed to execute Python code", e);
        }
    }
    
    /**
     * Execute Python code from a file.
     * 
     * @param filePath Path to the Python file
     * @return The output from the Python execution
     * @throws PythonExecutionException if execution fails
     */
    public String executeFile(String filePath) throws PythonExecutionException {
        logger.debug("Executing Python file: {}", filePath);
        
        try {
            Path path = Paths.get(filePath);
            
            if (!Files.exists(path)) {
                // Try to find the file in resources
                path = Paths.get("src/main/resources", filePath);
                if (!Files.exists(path)) {
                    throw new PythonExecutionException("Python file not found: " + filePath);
                }
            }
            
            String code = Files.readString(path);
            return executeCode(code);
            
        } catch (IOException e) {
            logger.error("Error reading Python file: {}", filePath, e);
            throw new PythonExecutionException("Failed to read Python file: " + filePath, e);
        }
    }
    
    /**
     * Execute Python code and return a specific variable value.
     *
     * @param code The Python code to execute
     * @param variableName The name of the variable to retrieve
     * @return The value of the specified variable
     * @throws PythonExecutionException if execution fails
     */
    public Object executeAndGetVariable(String code, String variableName) throws PythonExecutionException {
        logger.debug("Executing Python code and retrieving variable: {}", variableName);

        try {
            // Execute the code first
            executeCode(code);

            // Get the variable value from global variables
            if (!globalVariables.containsKey(variableName)) {
                throw new PythonExecutionException("Variable '" + variableName + "' not found after execution");
            }

            return globalVariables.get(variableName);

        } catch (Exception e) {
            logger.error("Error executing Python code and retrieving variable", e);
            throw new PythonExecutionException("Failed to execute Python code and retrieve variable", e);
        }
    }

    /**
     * Set a variable in the Python interpreter.
     *
     * @param variableName The name of the variable
     * @param value The value to set
     */
    public void setVariable(String variableName, Object value) {
        logger.debug("Setting Python variable: {} = {}", variableName, value);
        globalVariables.put(variableName, value);
    }

    /**
     * Close the Python interpreter and clean up resources.
     */
    public void close() {
        logger.debug("Closing Python executor");
        globalVariables.clear();
        // Runtime cleanup if needed
    }

    /**
     * Parse Python code into an AST using ANTLR4.
     *
     * @param code The Python code to parse
     * @return The parsed AST
     * @throws PythonExecutionException if parsing fails
     */
    private Program parseCode(String code) throws PythonExecutionException {
        try {
            // Create ANTLR input stream
            CharStream input = CharStreams.fromString(code);

            // Create lexer
            Python3Lexer lexer = new Python3Lexer(input);

            // Create token stream
            CommonTokenStream tokens = new CommonTokenStream(lexer);

            // Create parser
            Python3Parser parser = new Python3Parser(tokens);

            // Parse the input
            Python3Parser.File_inputContext tree = parser.file_input();

            // Build AST
            PythonASTBuilder astBuilder = new PythonASTBuilder();
            return (Program) astBuilder.visit(tree);

        } catch (Exception e) {
            logger.error("Error parsing Python code", e);
            throw new PythonExecutionException("Failed to parse Python code", e);
        }
    }
}
